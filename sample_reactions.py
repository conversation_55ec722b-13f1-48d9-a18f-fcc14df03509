"""
Example script to demonstrate running the virtual chemistry lab from another
Python script using command-line arguments.
"""
import subprocess
import sys
import os

def run_simulation(reactants: list, task: str):
    """
    Runs the main simulation script with the given reactants and task.
    """
    # Construct the path to main.py relative to this script's location
    main_script_path = os.path.join(os.path.dirname(__file__), '..', 'main.py')
    
    command = [sys.executable, main_script_path] + reactants + ['--task', task]
    
    print(f"\n--- Running simulation for: {' + '.join(reactants)} ---")
    print(f"Command: {' '.join(command)}")
    
    try:
        subprocess.run(command, check=True, text=True)
    except subprocess.CalledProcessError as e:
        print(f"Simulation failed with exit code {e.returncode}")
    except FileNotFoundError:
        print(f"Error: Could not find the main script at {main_script_path}")


if __name__ == '__main__':
    # --- Example 1: Esterification ---
    # Acetic Acid + Ethanol
    esterification_reactants = ["CC(=O)O", "CCO"]
    run_simulation(esterification_reactants, "predict_products")

    # --- Example 2: Diels-Alder ---
    # Butadiene + Ethene
    diels_alder_reactants = ["C=CC=C", "C=C"]
    run_simulation(diels_alder_reactants, "predict_products")
    
    # --- Example of a reaction not in the template library ---
    # Methane + Oxygen (will not find a template)
    combustion_reactants = ["[CH4]", "[O]=[O]"]
    run_simulation(combustion_reactants, "predict_products")