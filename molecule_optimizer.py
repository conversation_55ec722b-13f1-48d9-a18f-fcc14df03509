"""
Performs DFT-level geometry optimization and vibrational analysis.
"""
from ase import Atoms
from ase.optimize import BFGS
from ase.vibrations import Vibrations
from pyscf import gto, dft
import numpy as np
# Note: Using PySCF directly instead of ASE interface

def optimize_geometry(atoms: Atoms, xc: str, basis: str, solvent: str = None) -> Atoms:
    """
    Optimizes the geometry of an ASE Atoms object using PySCF.

    Args:
        atoms: The ASE Atoms object of the molecule to be optimized.
        xc: The exchange-correlation functional to use.
        basis: The basis set to use for the DFT calculation.
        solvent: The name of the implicit solvent model to use (e.g., 'Water').

    Returns:
        The optimized ASE Atoms object with the calculator attached.
    """
    if not isinstance(atoms, Atoms):
        raise TypeError("Input must be an ASE Atoms object.")

    print(f"Optimizing geometry for {atoms.get_chemical_formula()} with xc='{xc}', basis='{basis}'...")

    # Set up the PySCF molecule
    mol = gto.Mole()
    mol.atom = [[atom.symbol, atom.position] for atom in atoms]
    mol.basis = basis
    mol.build()

    # Set up the DFT method
    mf = dft.RKS(mol)
    mf.xc = xc
    
    # Add solvent model if specified
    if solvent:
        print(f"Applying implicit solvent model (PCM): {solvent}")
        mf = mf.with_solvent({'solvent_model': solvent})

    # For now, perform a simple single-point calculation instead of full optimization
    # This is a simplified implementation due to ASE-PySCF interface limitations
    print("Performing single-point DFT calculation...")

    try:
        # Run the DFT calculation
        energy = mf.kernel()
        print(f"DFT calculation completed for {atoms.get_chemical_formula()}.")
        print(f"Total energy: {energy:.6f} Hartree")

        # Store the energy in the atoms object for later use
        atoms.info['dft_energy'] = energy
        atoms.info['dft_method'] = f"{xc}/{basis}"

    except Exception as e:
        print(f"DFT calculation failed for {atoms.get_chemical_formula()}: {e}")
        atoms.info['dft_energy'] = None

    return atoms

def calculate_thermo_data(atoms: Atoms, advanced: bool = False, temperature: float = 298.15, solvent: str = None) -> dict:
    """
    Calculate thermochemical data with optional advanced properties.

    Args:
        atoms: An ASE Atoms object with DFT energy information.
        advanced: Whether to calculate advanced properties (frequencies, charges, etc.)
        temperature: Temperature in Kelvin for thermodynamic corrections
        solvent: Solvent for solvation calculations

    Returns:
        A dictionary containing thermochemical data.
    """
    print(f"Calculating thermochemical data for {atoms.get_chemical_formula()}...")

    # Basic thermochemical data
    thermo_data = {
        'formula': atoms.get_chemical_formula(),
        'energy_hartree': atoms.info.get('dft_energy', None),
        'method': atoms.info.get('dft_method', 'Unknown'),
        'num_atoms': len(atoms),
        'temperature': temperature
    }

    if thermo_data['energy_hartree'] is not None:
        # Convert Hartree to eV for consistency with other parts of the code
        thermo_data['energy_ev'] = thermo_data['energy_hartree'] * 27.2114  # Hartree to eV
        print(f"Energy: {thermo_data['energy_hartree']:.6f} Hartree ({thermo_data['energy_ev']:.4f} eV)")

        # Calculate advanced properties if requested
        if advanced:
            try:
                from simple_advanced_properties import SimpleAdvancedCalculator

                # Extract method details
                method_parts = thermo_data['method'].split('/')
                xc = method_parts[0] if len(method_parts) > 0 else 'b3lyp'
                basis = method_parts[1] if len(method_parts) > 1 else '6-31g*'

                # Initialize simplified advanced calculator
                calc = SimpleAdvancedCalculator(atoms, xc=xc, basis=basis)

                # Calculate all available properties
                advanced_props = calc.calculate_all_simple_properties(temperature=temperature)

                # Merge with basic data
                thermo_data.update(advanced_props)

                print("✅ Advanced properties calculated successfully!")

            except Exception as e:
                print(f"⚠️  Could not calculate advanced properties: {e}")
                print("Continuing with basic thermochemical data only.")
    else:
        print("No DFT energy available for thermochemical analysis.")

    return thermo_data