"""
Advanced Quantum Chemical Properties Calculator
Implements vibrational analysis, charge distribution, thermodynamic corrections, and more.
"""

import numpy as np
from ase import Atoms
from pyscf import gto, dft, hessian
from pyscf.tools import cubegen
import scipy.constants as const
from typing import Dict, List, Tuple, Optional

# Physical constants
HARTREE_TO_EV = 27.2114
HARTREE_TO_KCAL_MOL = 627.509
BOHR_TO_ANGSTROM = 0.529177
KB = const.k  # <PERSON><PERSON>mann constant in J/K
H = const.h   # Planck constant in J⋅s
C = const.c   # Speed of light in m/s
NA = const.N_A  # Avogadro's number

class AdvancedPropertiesCalculator:
    """
    Calculator for advanced quantum chemical properties.
    """
    
    def __init__(self, atoms: Atoms, xc: str = 'b3lyp', basis: str = '6-31g*'):
        """
        Initialize the calculator with molecular system.
        
        Args:
            atoms: ASE Atoms object
            xc: Exchange-correlation functional
            basis: Basis set
        """
        self.atoms = atoms
        self.xc = xc
        self.basis = basis
        self.mol = None
        self.mf = None
        self._setup_pyscf()
    
    def _setup_pyscf(self):
        """Setup PySCF molecule and mean-field object."""
        self.mol = gto.Mole()
        self.mol.atom = [[atom.symbol, atom.position] for atom in self.atoms]
        self.mol.basis = self.basis
        self.mol.build()
        
        self.mf = dft.RKS(self.mol)
        self.mf.xc = self.xc
        
        # Run SCF calculation
        self.mf.kernel()
    
    def calculate_vibrational_frequencies(self, temperature: float = 298.15) -> Dict:
        """
        Calculate vibrational frequencies and thermodynamic properties.
        
        Args:
            temperature: Temperature in Kelvin
            
        Returns:
            Dictionary containing vibrational analysis results
        """
        print(f"Calculating vibrational frequencies for {self.atoms.get_chemical_formula()}...")
        
        try:
            # Calculate Hessian matrix (second derivatives of energy)
            try:
                hess_calc = hessian.RKS(self.mf)
                hessian_matrix = hess_calc.kernel()
            except AttributeError:
                # Alternative method for different PySCF versions
                from pyscf import hessian as hess_module
                hess_calc = hess_module.rks.Hessian(self.mf)
                hessian_matrix = hess_calc.kernel()
            
            # Convert to mass-weighted coordinates
            masses = np.array([atom.mass for atom in self.atoms])
            mass_matrix = np.repeat(masses, 3)  # 3 coordinates per atom
            mass_weighted_hessian = hessian_matrix / np.sqrt(np.outer(mass_matrix, mass_matrix))
            
            # Diagonalize to get frequencies
            eigenvals, eigenvecs = np.linalg.eigh(mass_weighted_hessian)
            
            # Convert eigenvalues to frequencies (cm⁻¹)
            # Factor includes unit conversions
            freq_factor = np.sqrt(HARTREE_TO_EV * const.eV / (const.u * BOHR_TO_ANGSTROM**2)) / (2 * np.pi * C * 100)
            frequencies = np.sqrt(np.abs(eigenvals)) * freq_factor
            
            # Identify imaginary frequencies (negative eigenvalues)
            imaginary_freqs = frequencies[eigenvals < 0]
            real_freqs = frequencies[eigenvals >= 0]
            
            # Remove translational and rotational modes (should be ~0)
            real_freqs = real_freqs[real_freqs > 50]  # Threshold in cm⁻¹
            
            # Calculate thermodynamic properties
            thermo_props = self._calculate_thermodynamic_properties(real_freqs, temperature)
            
            results = {
                'frequencies_cm': real_freqs,
                'imaginary_frequencies': imaginary_freqs,
                'num_imaginary': len(imaginary_freqs),
                'zero_point_energy_hartree': thermo_props['zpe'],
                'zero_point_energy_ev': thermo_props['zpe'] * HARTREE_TO_EV,
                'thermal_energy_hartree': thermo_props['thermal_energy'],
                'entropy_cal_mol_k': thermo_props['entropy'],
                'free_energy_correction_hartree': thermo_props['free_energy_correction'],
                'temperature_k': temperature
            }
            
            print(f"✅ Found {len(real_freqs)} real vibrational modes")
            if len(imaginary_freqs) > 0:
                print(f"⚠️  Found {len(imaginary_freqs)} imaginary frequencies (transition state or unstable geometry)")
            print(f"Zero-point energy: {results['zero_point_energy_ev']:.4f} eV")
            
            return results
            
        except Exception as e:
            print(f"❌ Error calculating vibrational frequencies: {e}")
            return None
    
    def _calculate_thermodynamic_properties(self, frequencies: np.ndarray, temperature: float) -> Dict:
        """
        Calculate thermodynamic properties from vibrational frequencies.
        
        Args:
            frequencies: Vibrational frequencies in cm⁻¹
            temperature: Temperature in Kelvin
            
        Returns:
            Dictionary with thermodynamic properties
        """
        # Convert frequencies to energy units
        freq_hartree = frequencies * 100 * C * H / (HARTREE_TO_EV * const.eV)  # cm⁻¹ to Hartree
        
        # Zero-point energy
        zpe = 0.5 * np.sum(freq_hartree)
        
        # Thermal energy contribution from vibrations
        kT = KB * temperature / (HARTREE_TO_EV * const.eV)  # kT in Hartree
        
        # For each vibrational mode
        thermal_energy = 0.0
        entropy = 0.0
        
        for freq in freq_hartree:
            if freq > 0:
                x = freq / kT
                if x < 50:  # Avoid overflow
                    exp_x = np.exp(x)
                    thermal_energy += freq * x / (exp_x - 1)
                    entropy += x / (exp_x - 1) - np.log(1 - np.exp(-x))
        
        # Add translational and rotational contributions
        # Translational: 3/2 RT
        thermal_energy += 1.5 * kT
        
        # Rotational: 3/2 RT for nonlinear molecules, RT for linear
        if len(self.atoms) > 2:  # Assume nonlinear for now
            thermal_energy += 1.5 * kT
        else:
            thermal_energy += kT
        
        # Convert entropy to cal/mol⋅K
        entropy_cal_mol_k = entropy * KB * NA / 4.184
        
        # Free energy correction: G = H - TS
        free_energy_correction = thermal_energy - temperature * entropy * KB / (HARTREE_TO_EV * const.eV)
        
        return {
            'zpe': zpe,
            'thermal_energy': thermal_energy,
            'entropy': entropy_cal_mol_k,
            'free_energy_correction': free_energy_correction
        }
    
    def calculate_charge_distribution(self) -> Dict:
        """
        Calculate charge distribution using Mulliken population analysis.
        
        Returns:
            Dictionary containing charge analysis results
        """
        print(f"Calculating charge distribution for {self.atoms.get_chemical_formula()}...")
        
        try:
            # Try different ways to import Mulliken analysis
            try:
                from pyscf.tools import mulliken
                mulliken_charges = mulliken.mulliken_pop(self.mol, self.mf.mo_coeff, self.mf.mo_occ)
            except ImportError:
                # Alternative method
                from pyscf import lo
                mulliken_charges = lo.mulliken_pop(self.mol, self.mf.mo_coeff, self.mf.mo_occ)
            
            # Extract atomic charges
            atomic_charges = []
            if isinstance(mulliken_charges, tuple):
                # Handle different return formats
                pop_data = mulliken_charges[0] if len(mulliken_charges) > 0 else mulliken_charges
            else:
                pop_data = mulliken_charges

            for i, atom in enumerate(self.atoms):
                nuclear_charge = self.mol.atom_charge(i)
                electron_pop = pop_data[i] if hasattr(pop_data, '__getitem__') else 0
                charge = nuclear_charge - electron_pop
                atomic_charges.append(charge)
            
            results = {
                'mulliken_charges': atomic_charges,
                'total_charge': sum(atomic_charges),
                'dipole_moment': self._calculate_dipole_moment(),
                'most_positive_atom': np.argmax(atomic_charges),
                'most_negative_atom': np.argmin(atomic_charges),
                'charge_range': max(atomic_charges) - min(atomic_charges)
            }
            
            print(f"✅ Charge analysis complete")
            print(f"Most electrophilic site: Atom {results['most_positive_atom']} (charge: {max(atomic_charges):.3f})")
            print(f"Most nucleophilic site: Atom {results['most_negative_atom']} (charge: {min(atomic_charges):.3f})")
            
            return results
            
        except Exception as e:
            print(f"❌ Error calculating charge distribution: {e}")
            return None
    
    def _calculate_dipole_moment(self) -> float:
        """Calculate molecular dipole moment."""
        try:
            dipole = self.mf.dip_moment()
            return np.linalg.norm(dipole)
        except:
            return 0.0
    
    def calculate_solvation_energy(self, solvent: str = 'water') -> Dict:
        """
        Calculate solvation effects using PCM model.
        
        Args:
            solvent: Solvent name
            
        Returns:
            Dictionary containing solvation results
        """
        print(f"Calculating solvation effects in {solvent}...")
        
        try:
            # Gas phase energy (already calculated)
            gas_energy = self.mf.e_tot
            
            # Solvated calculation
            from pyscf import solvent
            
            # Create solvated mean-field object
            mf_solv = solvent.PCM(self.mf)
            if solvent.lower() == 'water':
                mf_solv.with_solvent.eps = 78.39  # Dielectric constant of water
            elif solvent.lower() == 'acetone':
                mf_solv.with_solvent.eps = 20.7
            elif solvent.lower() == 'dmso':
                mf_solv.with_solvent.eps = 46.7
            else:
                mf_solv.with_solvent.eps = 78.39  # Default to water
            
            solv_energy = mf_solv.kernel()
            
            solvation_energy = solv_energy - gas_energy
            
            results = {
                'gas_phase_energy_hartree': gas_energy,
                'solvated_energy_hartree': solv_energy,
                'solvation_energy_hartree': solvation_energy,
                'solvation_energy_kcal_mol': solvation_energy * HARTREE_TO_KCAL_MOL,
                'solvent': solvent
            }
            
            print(f"✅ Solvation energy in {solvent}: {results['solvation_energy_kcal_mol']:.2f} kcal/mol")
            
            return results
            
        except Exception as e:
            print(f"❌ Error calculating solvation effects: {e}")
            return None

    def calculate_temperature_effects(self, temperatures: List[float]) -> Dict:
        """
        Calculate temperature-dependent thermodynamic properties.

        Args:
            temperatures: List of temperatures in Kelvin

        Returns:
            Dictionary containing temperature-dependent results
        """
        print(f"Calculating temperature effects for {self.atoms.get_chemical_formula()}...")

        # First need vibrational frequencies
        freq_results = self.calculate_vibrational_frequencies(temperature=298.15)
        if freq_results is None:
            return None

        frequencies = freq_results['frequencies_cm']

        results = {
            'temperatures': temperatures,
            'free_energies': [],
            'enthalpies': [],
            'entropies': [],
            'heat_capacities': []
        }

        for T in temperatures:
            thermo = self._calculate_thermodynamic_properties(frequencies, T)

            # Electronic energy + ZPE + thermal corrections
            enthalpy = self.mf.e_tot + thermo['zpe'] + thermo['thermal_energy']
            free_energy = enthalpy + thermo['free_energy_correction']

            results['free_energies'].append(free_energy)
            results['enthalpies'].append(enthalpy)
            results['entropies'].append(thermo['entropy'])

            # Heat capacity (approximate from frequency analysis)
            cp = self._calculate_heat_capacity(frequencies, T)
            results['heat_capacities'].append(cp)

        print(f"✅ Temperature effects calculated for {len(temperatures)} temperatures")
        return results

    def _calculate_heat_capacity(self, frequencies: np.ndarray, temperature: float) -> float:
        """Calculate heat capacity from vibrational frequencies."""
        freq_hartree = frequencies * 100 * C * H / (HARTREE_TO_EV * const.eV)
        kT = KB * temperature / (HARTREE_TO_EV * const.eV)

        cp_vib = 0.0
        for freq in freq_hartree:
            if freq > 0:
                x = freq / kT
                if x < 50:
                    exp_x = np.exp(x)
                    cp_vib += x**2 * exp_x / (exp_x - 1)**2

        # Add translational (3/2 R) and rotational contributions
        cp_total = cp_vib + 2.5  # in units of R

        # Convert to cal/mol⋅K
        return cp_total * const.R / 4.184

    def analyze_frontier_orbitals(self) -> Dict:
        """
        Analyze HOMO and LUMO energies and gaps.

        Returns:
            Dictionary containing orbital analysis
        """
        print(f"Analyzing frontier orbitals for {self.atoms.get_chemical_formula()}...")

        try:
            # Get orbital energies
            orbital_energies = self.mf.mo_energy
            occupations = self.mf.mo_occ

            # Find HOMO and LUMO
            occupied_orbitals = orbital_energies[occupations > 0]
            virtual_orbitals = orbital_energies[occupations == 0]

            homo_energy = np.max(occupied_orbitals) if len(occupied_orbitals) > 0 else None
            lumo_energy = np.min(virtual_orbitals) if len(virtual_orbitals) > 0 else None

            results = {
                'homo_energy_hartree': homo_energy,
                'lumo_energy_hartree': lumo_energy,
                'homo_energy_ev': homo_energy * HARTREE_TO_EV if homo_energy else None,
                'lumo_energy_ev': lumo_energy * HARTREE_TO_EV if lumo_energy else None,
                'homo_lumo_gap_ev': (lumo_energy - homo_energy) * HARTREE_TO_EV if (homo_energy and lumo_energy) else None,
                'ionization_potential_ev': -homo_energy * HARTREE_TO_EV if homo_energy else None,
                'electron_affinity_ev': -lumo_energy * HARTREE_TO_EV if lumo_energy else None
            }

            if results['homo_lumo_gap_ev']:
                print(f"✅ HOMO-LUMO gap: {results['homo_lumo_gap_ev']:.3f} eV")
                print(f"Ionization potential: {results['ionization_potential_ev']:.3f} eV")
                print(f"Electron affinity: {results['electron_affinity_ev']:.3f} eV")

            return results

        except Exception as e:
            print(f"❌ Error analyzing frontier orbitals: {e}")
            return None

    def calculate_all_properties(self, temperature: float = 298.15, solvent: Optional[str] = None) -> Dict:
        """
        Calculate all available advanced properties.

        Args:
            temperature: Temperature in Kelvin
            solvent: Solvent for solvation calculation

        Returns:
            Dictionary containing all calculated properties
        """
        print(f"\n🔬 Calculating all advanced properties for {self.atoms.get_chemical_formula()}")
        print("=" * 60)

        results = {
            'basic_info': {
                'formula': self.atoms.get_chemical_formula(),
                'num_atoms': len(self.atoms),
                'method': f"{self.xc}/{self.basis}",
                'scf_energy_hartree': self.mf.e_tot,
                'scf_energy_ev': self.mf.e_tot * HARTREE_TO_EV
            }
        }

        # Vibrational analysis
        results['vibrational'] = self.calculate_vibrational_frequencies(temperature)

        # Charge distribution
        results['charge_distribution'] = self.calculate_charge_distribution()

        # Frontier orbitals
        results['frontier_orbitals'] = self.analyze_frontier_orbitals()

        # Temperature effects
        temp_range = [200, 250, 298.15, 350, 400, 500]
        results['temperature_effects'] = self.calculate_temperature_effects(temp_range)

        # Solvation effects (if requested)
        if solvent:
            results['solvation'] = self.calculate_solvation_energy(solvent)

        print("=" * 60)
        print("🎉 All advanced properties calculated successfully!")

        return results
