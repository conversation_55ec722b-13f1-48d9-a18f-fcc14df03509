"""
A diagnostic script to find the correct import path for a function
within a library, in this case, 'load_model' in 'schnetpack'.
"""
import pkgutil
import importlib
import inspect
import schnetpack
import os

# Suppress annoying warnings for this diagnostic script
os.environ['PYTHONWARNINGS'] = 'ignore'

def find_function_in_package(package, function_name):
    """
    Recursively searches for a function within a Python package.
    """
    print(f"Searching for '{function_name}' in package '{package.__name__}'...")
    
    # Get the path of the installed package
    package_path = package.__path__
    package_prefix = package.__name__ + '.'

    # Walk through all modules and sub-modules in the package
    for importer, modname, ispkg in pkgutil.walk_packages(path=package_path,
                                                          prefix=package_prefix,
                                                          onerror=lambda x: None):
        try:
            # Try to import the module
            module = importlib.import_module(modname)
            # Check if the function exists in this module
            if hasattr(module, function_name):
                obj = getattr(module, function_name)
                # Make sure it's a function we're finding
                if inspect.isfunction(obj):
                    print("\n" + "="*60)
                    print(f"🎉 SUCCESS: Found '{function_name}'!")
                    print(f"   The correct import statement for your installed version is:")
                    print(f"   from {modname} import {function_name}")
                    print("="*60 + "\n")
                    return
        except Exception:
            # Ignore any errors from modules that fail to import
            pass
            
    # If the loop finishes without finding the function
    print("\n" + "="*60)
    print(f"❌ FAILURE: Could not automatically find the '{function_name}' function.")
    print("   This suggests a severe installation problem with schnetpack or a major,")
    print("   breaking API change. Please consider reinstalling with:")
    print("   pip install --force-reinstall schnetpack")
    print("="*60 + "\n")


if __name__ == "__main__":
    find_function_in_package(schnetpack, 'load_model')