"""
Simplified Advanced Properties Calculator
Focuses on properties that work reliably with the current PySCF installation.
"""

import numpy as np
from ase import Atoms
from pyscf import gto, dft
import scipy.constants as const
from typing import Dict, List, Optional

# Physical constants
HARTREE_TO_EV = 27.2114
HARTREE_TO_KCAL_MOL = 627.509
KB = const.k  # Boltzmann constant in J/K
NA = const.N_A  # Avogadro's number

class SimpleAdvancedCalculator:
    """
    Simplified calculator for quantum chemical properties that work reliably.
    """
    
    def __init__(self, atoms: Atoms, xc: str = 'b3lyp', basis: str = '6-31g*'):
        """Initialize the calculator."""
        self.atoms = atoms
        self.xc = xc
        self.basis = basis
        self.mol = None
        self.mf = None
        self._setup_pyscf()
    
    def _setup_pyscf(self):
        """Setup PySCF calculation."""
        self.mol = gto.Mole()
        self.mol.atom = [[atom.symbol, atom.position] for atom in self.atoms]
        self.mol.basis = self.basis
        self.mol.build()
        
        self.mf = dft.RKS(self.mol)
        self.mf.xc = self.xc
        self.mf.kernel()
    
    def analyze_frontier_orbitals(self) -> Dict:
        """Analyze HOMO and LUMO energies."""
        print(f"🔬 Analyzing frontier orbitals for {self.atoms.get_chemical_formula()}...")
        
        try:
            orbital_energies = self.mf.mo_energy
            occupations = self.mf.mo_occ
            
            occupied_orbitals = orbital_energies[occupations > 0]
            virtual_orbitals = orbital_energies[occupations == 0]
            
            homo_energy = np.max(occupied_orbitals) if len(occupied_orbitals) > 0 else None
            lumo_energy = np.min(virtual_orbitals) if len(virtual_orbitals) > 0 else None
            
            results = {
                'homo_energy_hartree': homo_energy,
                'lumo_energy_hartree': lumo_energy,
                'homo_energy_ev': homo_energy * HARTREE_TO_EV if homo_energy else None,
                'lumo_energy_ev': lumo_energy * HARTREE_TO_EV if lumo_energy else None,
                'homo_lumo_gap_ev': (lumo_energy - homo_energy) * HARTREE_TO_EV if (homo_energy and lumo_energy) else None,
                'ionization_potential_ev': -homo_energy * HARTREE_TO_EV if homo_energy else None,
                'electron_affinity_ev': -lumo_energy * HARTREE_TO_EV if lumo_energy else None
            }
            
            print(f"✅ HOMO-LUMO gap: {results['homo_lumo_gap_ev']:.3f} eV")
            print(f"   Ionization potential: {results['ionization_potential_ev']:.3f} eV")
            print(f"   Electron affinity: {results['electron_affinity_ev']:.3f} eV")
            
            return results
            
        except Exception as e:
            print(f"❌ Error analyzing frontier orbitals: {e}")
            return None
    
    def calculate_dipole_moment(self) -> Dict:
        """Calculate molecular dipole moment."""
        print(f"🔬 Calculating dipole moment for {self.atoms.get_chemical_formula()}...")
        
        try:
            dipole_vector = self.mf.dip_moment()
            dipole_magnitude = np.linalg.norm(dipole_vector)
            
            results = {
                'dipole_vector_au': dipole_vector,
                'dipole_magnitude_au': dipole_magnitude,
                'dipole_magnitude_debye': dipole_magnitude * 2.5417  # Convert to Debye
            }
            
            print(f"✅ Dipole moment: {results['dipole_magnitude_debye']:.3f} Debye")
            
            return results
            
        except Exception as e:
            print(f"❌ Error calculating dipole moment: {e}")
            return None
    
    def analyze_electron_density(self) -> Dict:
        """Analyze electron density properties."""
        print(f"🔬 Analyzing electron density for {self.atoms.get_chemical_formula()}...")
        
        try:
            # Get density matrix
            dm = self.mf.make_rdm1()
            
            # Calculate total electron density at nuclei
            nuclear_densities = []
            for i, atom in enumerate(self.atoms):
                coords = atom.position.reshape(1, 3)
                density_at_nucleus = self.mf.get_rho(dm, coords)[0]
                nuclear_densities.append(density_at_nucleus)
            
            results = {
                'nuclear_densities': nuclear_densities,
                'max_nuclear_density': max(nuclear_densities),
                'min_nuclear_density': min(nuclear_densities),
                'total_electrons': np.trace(dm @ self.mf.get_ovlp())
            }
            
            print(f"✅ Total electrons: {results['total_electrons']:.1f}")
            print(f"   Max nuclear density: {results['max_nuclear_density']:.3f}")
            
            return results
            
        except Exception as e:
            print(f"❌ Error analyzing electron density: {e}")
            return None
    
    def estimate_thermodynamic_properties(self, temperature: float = 298.15) -> Dict:
        """
        Estimate basic thermodynamic properties using simple models.
        """
        print(f"🔬 Estimating thermodynamic properties at {temperature} K...")
        
        try:
            # Basic electronic energy
            electronic_energy = self.mf.e_tot
            
            # Estimate vibrational contribution using simple harmonic oscillator model
            # This is a rough approximation based on molecular size and bonds
            num_atoms = len(self.atoms)
            num_bonds = max(1, num_atoms - 1)  # Rough estimate
            
            # Typical vibrational frequency ~1000 cm⁻¹ = 0.124 eV
            typical_vib_energy_ev = 0.124
            typical_vib_energy_hartree = typical_vib_energy_ev / HARTREE_TO_EV
            
            # Zero-point energy (rough estimate)
            zpe_estimate = 0.5 * num_bonds * typical_vib_energy_hartree
            
            # Thermal energy contributions
            kT_hartree = KB * temperature / (HARTREE_TO_EV * const.eV)
            
            # Translational: 3/2 kT
            # Rotational: 3/2 kT (nonlinear) or kT (linear)
            # Vibrational: estimated from bonds
            thermal_correction = 3.0 * kT_hartree  # Rough estimate
            
            # Entropy estimate (very rough)
            entropy_estimate = 50.0 + 10.0 * num_atoms  # cal/mol⋅K
            
            results = {
                'electronic_energy_hartree': electronic_energy,
                'zpe_estimate_hartree': zpe_estimate,
                'thermal_correction_hartree': thermal_correction,
                'enthalpy_estimate_hartree': electronic_energy + zpe_estimate + thermal_correction,
                'entropy_estimate_cal_mol_k': entropy_estimate,
                'free_energy_estimate_hartree': electronic_energy + zpe_estimate + thermal_correction - temperature * entropy_estimate * 4.184 / (NA * HARTREE_TO_EV * const.eV),
                'temperature_k': temperature
            }
            
            print(f"✅ Estimated ZPE: {zpe_estimate * HARTREE_TO_EV:.3f} eV")
            print(f"   Estimated thermal correction: {thermal_correction * HARTREE_TO_EV:.3f} eV")
            print(f"   Estimated entropy: {entropy_estimate:.1f} cal/mol⋅K")
            
            return results
            
        except Exception as e:
            print(f"❌ Error estimating thermodynamic properties: {e}")
            return None
    
    def calculate_all_simple_properties(self, temperature: float = 298.15) -> Dict:
        """Calculate all available simple properties."""
        print(f"\n🔬 Calculating simplified advanced properties for {self.atoms.get_chemical_formula()}")
        print("=" * 60)
        
        results = {
            'basic_info': {
                'formula': self.atoms.get_chemical_formula(),
                'num_atoms': len(self.atoms),
                'method': f"{self.xc}/{self.basis}",
                'scf_energy_hartree': self.mf.e_tot,
                'scf_energy_ev': self.mf.e_tot * HARTREE_TO_EV
            }
        }
        
        # Calculate each property
        results['frontier_orbitals'] = self.analyze_frontier_orbitals()
        results['dipole_moment'] = self.calculate_dipole_moment()
        results['electron_density'] = self.analyze_electron_density()
        results['thermodynamic_estimates'] = self.estimate_thermodynamic_properties(temperature)
        
        print("=" * 60)
        print("🎉 Simplified advanced properties calculated!")
        
        return results
