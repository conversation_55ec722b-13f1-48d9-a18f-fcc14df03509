"""
Calculates thermodynamic and kinetic properties of a reaction.
"""
from ase.vibrations import Vibrations
from ase.neb import NEB
import numpy as np

# Physical constants
kB = 8.617333262145e-5  # <PERSON><PERSON>mann constant in eV/K

def calculate_gibbs_free_energy(vib_data: Vibrations, temperature: float = 298.15, pressure: float = 101325.0) -> float:
    """
    Calculates the Gibbs Free Energy from vibrational data.

    Args:
        vib_data: A converged ASE Vibrations object.
        temperature: Temperature in Kelvin.
        pressure: Pressure in Pascals.

    Returns:
        The Gibbs Free Energy in eV.
    """
    if not vib_data:
        return float('nan') # Return Not a Number if vibrational analysis failed
    # The get_free_energy method includes ZPE, electronic, and thermal contributions
    free_energy = vib_data.get_free_energy(temperature=temperature, pressure=pressure, verbose=False)
    return free_energy

def check_thermodynamic_feasibility_G(g_reactants: float, g_products: float) -> tuple[bool, float]:
    """
    Checks if a reaction is thermodynamically favorable based on Gibbs Free Energy (ΔG).

    Args:
        g_reactants: Total Gibbs Free Energy of all reactants.
        g_products: Total Gibbs Free Energy of all products.

    Returns:
        A tuple containing a boolean (True if spontaneous) and the ΔG value.
    """
    delta_g = g_products - g_reactants
    is_spontaneous = delta_g < 0
    print(f"\n--- Thermodynamic Analysis (Gibbs Free Energy) ---")
    print(f"G(Reactants): {g_reactants:.4f} eV")
    print(f"G(Products):  {g_products:.4f} eV")
    print(f"ΔG = {delta_g:.4f} eV")
    if is_spontaneous:
        print("Result: The reaction is thermodynamically spontaneous (exergonic).")
    else:
        print("Result: The reaction is not thermodynamically spontaneous (endergonic).")
    return is_spontaneous, delta_g

def calculate_activation_energy(neb: NEB) -> float:
    """
    Calculates the activation energy (Ea) from a NEB calculation.

    Args:
        neb: The converged NEB object.

    Returns:
        The activation energy in eV.
    """
    if not neb or not hasattr(neb, 'images'):
        return 0.0

    energies = [image.get_potential_energy() for image in neb.images]
    reactant_energy = energies[0]
    highest_energy = max(energies)
    
    activation_energy = highest_energy - reactant_energy
    
    print(f"\n--- Kinetic Analysis ---")
    print(f"Activation Energy (Ea): {activation_energy:.4f} eV")
    
    return activation_energy

def calculate_arrhenius_rate_constant(activation_energy: float, temperature: float) -> float:
    """
    Calculates the reaction rate constant (k) using the Arrhenius equation.

    Args:
        activation_energy: The activation energy (Ea) in eV.
        temperature: The temperature in Kelvin.

    Returns:
        The reaction rate constant k. The units depend on the pre-factor A.
    """
    # Pre-exponential factor 'A' (frequency factor).
    # This is an empirical constant that ideally comes from Transition State Theory,
    # involving vibrational frequencies of the reactant and transition state.
    # We'll use a typical value for unimolecular reactions (~10^13 s^-1) as a placeholder.
    A = 1e13  # s^-1
    
    if activation_energy < 0:
        activation_energy = 0 # Rate cannot be more than A

    rate_constant = A * np.exp(-activation_energy / (kB * temperature))
    print(f"Arrhenius Rate Constant (k) at {temperature} K: {rate_constant:.4e} s^-1")
    return rate_constant