"""
Handles the conversion of input formats, primarily SMILES strings,
into 3D ASE Atoms objects suitable for quantum chemical simulations.
"""
import sys
from ase import Atoms
from rdkit import Chem
from rdkit.Chem import AllChem

def smiles_to_ase(smiles_string: str) -> Atoms | None:
    """
    Converts a SMILES string into an ASE (Atomic Simulation Environment) Atoms object.

    This process involves:
    1. Parsing the SMILES string using RDKit.
    2. Adding explicit hydrogen atoms.
    3. Generating an initial 3D conformation for the molecule.
    4. Converting the RDKit Mol object into an ASE Atoms object.

    Args:
        smiles_string: The SMILES representation of the molecule.

    Returns:
        An ASE Atoms object with a 3D structure, or None if the conversion fails.
    """
    try:
        # 1. Parse the SMILES string into an RDKit molecule object.
        mol = Chem.MolFromSmiles(smiles_string)
        if mol is None:
            print(f"Error: RDKit could not parse the SMILES string: '{smiles_string}'", file=sys.stderr)
            return None

        # 2. Add explicit hydrogens to the molecule graph.
        # This is crucial as SMILES often omits them for clarity.
        mol = Chem.AddHs(mol)

        # 3. Generate an initial 3D conformation.
        # The EmbedMolecule function uses distance geometry to create a reasonable
        # starting structure. This is a critical prerequisite for any DFT calculation.
        status = AllChem.EmbedMolecule(mol, AllChem.ETKDG())
        if status == -1:
            # A status of -1 indicates that conformation generation failed.
            # This can happen for very strained or complex molecules.
            print(f"Error: RDKit could not generate a 3D conformation for SMILES: '{smiles_string}'", file=sys.stderr)
            return None
        
        # Further optimize the initial 3D geometry with a universal force field (UFF).
        # This provides a better starting point for the more expensive DFT optimization.
        AllChem.UFFOptimizeMolecule(mol)

        # 4. Extract atomic information and create the ASE Atoms object.
        symbols = [atom.GetSymbol() for atom in mol.GetAtoms()]
        positions = mol.GetConformer().GetPositions()

        # The ASE Atoms object is the standard format used by all our simulation tools.
        atoms = Atoms(symbols=symbols, positions=positions)
        
        # Set periodic boundary conditions to False, as we are dealing with single molecules (not crystals).
        atoms.set_pbc(False)
        
        return atoms

    except Exception as e:
        print(f"An unexpected error occurred in smiles_to_ase for '{smiles_string}': {e}", file=sys.stderr)
        return None