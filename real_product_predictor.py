"""
Real Product Prediction System
Uses multiple approaches: ML models, reaction templates, and chemical rules
No hardcoded or dummy data - all predictions are computed.
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import AllChem, Descriptors, rdMolDescriptors
from rdkit.Chem.rdchem import BondType
from typing import List, Dict, Tuple, Optional, Set
import itertools
from collections import defaultdict
import re

class RealProductPredictor:
    """
    Professional product prediction system using multiple approaches.
    """
    
    def __init__(self):
        """Initialize the predictor with reaction templates and rules."""
        self.reaction_templates = self._load_reaction_templates()
        self.functional_group_patterns = self._load_functional_groups()
        self.bond_formation_rules = self._load_bond_formation_rules()
        
    def _load_reaction_templates(self) -> Dict[str, Dict]:
        """
        Load common reaction templates based on functional group interactions.
        These are real chemical reaction patterns, not hardcoded products.
        """
        templates = {
            'esterification': {
                'reactants': ['carboxylic_acid', 'alcohol'],
                'pattern': '[C:1](=[O:2])[OH:3].[OH:4][C:5]>>[C:1](=[O:2])[O:4][C:5].[OH2:3]',
                'description': 'Carboxylic acid + Alcohol → Ester + Water',
                'conditions': {'temperature': 'moderate', 'catalyst': 'acid'}
            },
            'amidation': {
                'reactants': ['carboxylic_acid', 'amine'],
                'pattern': '[C:1](=[O:2])[OH:3].[NH2:4][C:5]>>[C:1](=[O:2])[NH:4][C:5].[OH2:3]',
                'description': 'Carboxylic acid + Amine → Amide + Water',
                'conditions': {'temperature': 'high', 'catalyst': 'coupling_agent'}
            },
            'aldol_condensation': {
                'reactants': ['aldehyde', 'ketone'],
                'pattern': '[CH2:1][C:2]=[O:3].[C:4][CH2:5][C:6]=[O:7]>>[C:4][CH:5]=[C:2][C:1]=[O:3].[OH2]',
                'description': 'Aldehyde + Ketone → α,β-unsaturated carbonyl + Water',
                'conditions': {'temperature': 'moderate', 'catalyst': 'base'}
            },
            'nucleophilic_substitution': {
                'reactants': ['alkyl_halide', 'nucleophile'],
                'pattern': '[C:1][Cl,Br,I:2].[OH,NH2,SH:3]>>[C:1][*:3].[Cl,Br,I:2]',
                'description': 'Alkyl halide + Nucleophile → Substituted product + Halide',
                'conditions': {'temperature': 'moderate', 'solvent': 'polar'}
            },
            'diels_alder': {
                'reactants': ['diene', 'dienophile'],
                'pattern': '[C:1]=[C:2][C:3]=[C:4].[C:5]=[C:6]>>[C:1]1[C:2][C:5][C:6][C:4][C:3]1',
                'description': 'Diene + Dienophile → Cyclohexene derivative',
                'conditions': {'temperature': 'moderate_to_high', 'solvent': 'nonpolar'}
            },
            'grignard_addition': {
                'reactants': ['carbonyl', 'grignard'],
                'pattern': '[C:1]=[O:2].[C:3][Mg][Br:4]>>[C:1]([OH:2])[C:3].[Mg][Br:4]',
                'description': 'Carbonyl + Grignard → Alcohol + MgBr',
                'conditions': {'temperature': 'low', 'solvent': 'ether', 'atmosphere': 'inert'}
            }
        }
        return templates
    
    def _load_functional_groups(self) -> Dict[str, str]:
        """Load SMARTS patterns for functional group recognition."""
        return {
            'carboxylic_acid': '[CX3](=O)[OX2H1]',
            'alcohol': '[CX4][OX2H]',
            'aldehyde': '[CX3H1](=O)[#6]',
            'ketone': '[CX3](=O)([#6])[#6]',
            'amine': '[NX3;H2,H1;!$(NC=O)]',
            'alkyl_halide': '[CX4][F,Cl,Br,I]',
            'ester': '[CX3](=O)[OX2H0]',
            'amide': '[CX3](=O)[NX3]',
            'diene': '[CX3]=[CX3][CX3]=[CX3]',
            'dienophile': '[CX3]=[CX3]',
            'grignard': '[C][Mg][Br,Cl,I]',
            'carbonyl': '[CX3]=[OX1]',
            'aromatic': 'c1ccccc1',
            'alkene': '[CX3]=[CX3]',
            'alkyne': '[CX2]#[CX2]'
        }
    
    def _load_bond_formation_rules(self) -> Dict[str, List[str]]:
        """Define which functional groups can form bonds with each other."""
        return {
            'carboxylic_acid': ['alcohol', 'amine'],
            'alcohol': ['carboxylic_acid', 'alkyl_halide'],
            'aldehyde': ['amine', 'alcohol', 'grignard'],
            'ketone': ['amine', 'alcohol', 'grignard'],
            'amine': ['carboxylic_acid', 'aldehyde', 'ketone', 'alkyl_halide'],
            'alkyl_halide': ['alcohol', 'amine'],
            'diene': ['dienophile'],
            'dienophile': ['diene'],
            'grignard': ['carbonyl', 'aldehyde', 'ketone']
        }
    
    def identify_functional_groups(self, mol: Chem.Mol) -> List[str]:
        """
        Identify all functional groups in a molecule.
        
        Args:
            mol: RDKit molecule object
            
        Returns:
            List of functional group names
        """
        if mol is None:
            return []
        
        functional_groups = []
        
        for fg_name, pattern in self.functional_group_patterns.items():
            if mol.HasSubstructMatch(Chem.MolFromSmarts(pattern)):
                functional_groups.append(fg_name)
        
        return functional_groups
    
    def predict_products_from_templates(self, reactant_smiles: List[str]) -> List[Dict]:
        """
        Predict products using reaction templates.
        
        Args:
            reactant_smiles: List of SMILES strings for reactants
            
        Returns:
            List of predicted reaction outcomes
        """
        if len(reactant_smiles) < 2:
            return []
        
        # Convert SMILES to molecules
        reactant_mols = []
        for smiles in reactant_smiles:
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                reactant_mols.append(mol)
        
        if len(reactant_mols) < 2:
            return []
        
        # Identify functional groups in each reactant
        reactant_fgs = []
        for mol in reactant_mols:
            fgs = self.identify_functional_groups(mol)
            reactant_fgs.append(fgs)
        
        print(f"🔍 Identified functional groups:")
        for i, fgs in enumerate(reactant_fgs):
            print(f"   Reactant {i+1}: {fgs}")
        
        # Find matching reaction templates
        possible_reactions = []
        
        for template_name, template_data in self.reaction_templates.items():
            required_fgs = template_data['reactants']
            
            # Check if we have the required functional groups
            if self._check_functional_group_match(reactant_fgs, required_fgs):
                print(f"✅ Found matching template: {template_name}")
                
                # Try to apply the reaction template
                products = self._apply_reaction_template(
                    reactant_mols, 
                    template_data['pattern'],
                    template_name
                )
                
                if products:
                    reaction_outcome = {
                        'reaction_type': template_name,
                        'description': template_data['description'],
                        'reactants': reactant_smiles,
                        'products': products,
                        'conditions': template_data['conditions'],
                        'confidence': self._calculate_confidence(template_name, reactant_fgs)
                    }
                    possible_reactions.append(reaction_outcome)
        
        # Sort by confidence
        possible_reactions.sort(key=lambda x: x['confidence'], reverse=True)
        
        return possible_reactions
    
    def _check_functional_group_match(self, reactant_fgs: List[List[str]], 
                                    required_fgs: List[str]) -> bool:
        """Check if reactants have the required functional groups for a template."""
        if len(required_fgs) != len(reactant_fgs):
            return False
        
        # Try all permutations of reactants
        for perm in itertools.permutations(range(len(reactant_fgs))):
            match = True
            for i, required_fg in enumerate(required_fgs):
                if required_fg not in reactant_fgs[perm[i]]:
                    match = False
                    break
            if match:
                return True
        
        return False
    
    def _apply_reaction_template(self, reactant_mols: List[Chem.Mol], 
                               pattern: str, template_name: str) -> List[str]:
        """
        Apply a reaction template to predict products.
        
        Args:
            reactant_mols: List of reactant molecules
            pattern: SMIRKS reaction pattern
            template_name: Name of the reaction template
            
        Returns:
            List of product SMILES
        """
        try:
            # Parse the SMIRKS pattern
            reaction = AllChem.ReactionFromSmarts(pattern)
            if reaction is None:
                return []
            
            # Apply the reaction
            products_tuples = reaction.RunReactants(reactant_mols)
            
            if not products_tuples:
                return []
            
            # Convert products to SMILES
            product_smiles = []
            for products_tuple in products_tuples[:1]:  # Take first outcome
                for product_mol in products_tuple:
                    if product_mol:
                        try:
                            Chem.SanitizeMol(product_mol)
                            smiles = Chem.MolToSmiles(product_mol)
                            if smiles and smiles not in product_smiles:
                                product_smiles.append(smiles)
                        except:
                            continue
            
            return product_smiles
            
        except Exception as e:
            print(f"⚠️  Error applying template {template_name}: {e}")
            return []
    
    def _calculate_confidence(self, template_name: str, reactant_fgs: List[List[str]]) -> float:
        """
        Calculate confidence score for a predicted reaction.
        
        Args:
            template_name: Name of the reaction template
            reactant_fgs: Functional groups in reactants
            
        Returns:
            Confidence score (0-1)
        """
        base_confidence = {
            'esterification': 0.9,
            'amidation': 0.85,
            'nucleophilic_substitution': 0.8,
            'aldol_condensation': 0.75,
            'diels_alder': 0.9,
            'grignard_addition': 0.85
        }
        
        confidence = base_confidence.get(template_name, 0.5)
        
        # Adjust based on functional group specificity
        total_fgs = sum(len(fgs) for fgs in reactant_fgs)
        if total_fgs > 4:  # Many functional groups = lower confidence
            confidence *= 0.8
        elif total_fgs < 2:  # Few functional groups = lower confidence
            confidence *= 0.7
        
        return confidence

    def predict_products_ml_enhanced(self, reactant_smiles: List[str]) -> List[Dict]:
        """
        Enhanced product prediction using ML model (ReactionT5) + templates.

        Args:
            reactant_smiles: List of SMILES strings for reactants

        Returns:
            List of predicted reaction outcomes with multiple approaches
        """
        print(f"🤖 Starting ML-enhanced product prediction...")

        all_predictions = []

        # 1. Template-based predictions
        template_predictions = self.predict_products_from_templates(reactant_smiles)
        for pred in template_predictions:
            pred['method'] = 'template_based'
            all_predictions.append(pred)

        # 2. ML model predictions (using existing ReactionT5)
        try:
            from product_predictor import predict_products
            # Use the existing ML model with correct method
            ml_products = predict_products(reactant_smiles)

            if ml_products:
                for i, product_smiles in enumerate(ml_products):
                    # Analyze the ML prediction
                    reaction_type = self._classify_reaction_type(reactant_smiles, [product_smiles])

                    ml_prediction = {
                        'reaction_type': reaction_type,
                        'description': f'ML-predicted {reaction_type}',
                        'reactants': reactant_smiles,
                        'products': [product_smiles],
                        'conditions': self._suggest_conditions_for_reaction(reaction_type),
                        'confidence': 0.8 - (i * 0.1),  # Decrease confidence for later predictions
                        'method': 'ml_model'
                    }
                    all_predictions.append(ml_prediction)

        except Exception as e:
            print(f"⚠️  ML prediction failed: {e}")

        # 3. Remove duplicates and rank by confidence
        unique_predictions = self._remove_duplicate_predictions(all_predictions)
        unique_predictions.sort(key=lambda x: x['confidence'], reverse=True)

        return unique_predictions

    def _classify_reaction_type(self, reactants: List[str], products: List[str]) -> str:
        """
        Classify the reaction type based on reactants and products.

        Args:
            reactants: List of reactant SMILES
            products: List of product SMILES

        Returns:
            Reaction type classification
        """
        # Convert to molecules for analysis
        reactant_mols = [Chem.MolFromSmiles(s) for s in reactants if Chem.MolFromSmiles(s)]
        product_mols = [Chem.MolFromSmiles(s) for s in products if Chem.MolFromSmiles(s)]

        if not reactant_mols or not product_mols:
            return 'unknown'

        # Analyze functional group changes
        reactant_fgs = []
        for mol in reactant_mols:
            reactant_fgs.extend(self.identify_functional_groups(mol))

        product_fgs = []
        for mol in product_mols:
            product_fgs.extend(self.identify_functional_groups(mol))

        # Classify based on functional group transformations
        if 'carboxylic_acid' in reactant_fgs and 'alcohol' in reactant_fgs and 'ester' in product_fgs:
            return 'esterification'
        elif 'carboxylic_acid' in reactant_fgs and 'amine' in reactant_fgs and 'amide' in product_fgs:
            return 'amidation'
        elif 'alkyl_halide' in reactant_fgs and len(product_mols) > len(reactant_mols):
            return 'nucleophilic_substitution'
        elif 'diene' in reactant_fgs and 'dienophile' in reactant_fgs:
            return 'diels_alder'
        elif 'aldehyde' in reactant_fgs or 'ketone' in reactant_fgs:
            if 'grignard' in reactant_fgs:
                return 'grignard_addition'
            elif 'alcohol' in product_fgs:
                return 'reduction'
            else:
                return 'carbonyl_reaction'
        else:
            return 'general_organic'

    def _suggest_conditions_for_reaction(self, reaction_type: str) -> Dict[str, str]:
        """Suggest reaction conditions based on reaction type."""
        condition_map = {
            'esterification': {'temperature': 'moderate', 'catalyst': 'acid', 'solvent': 'organic'},
            'amidation': {'temperature': 'high', 'catalyst': 'coupling_agent', 'solvent': 'aprotic'},
            'nucleophilic_substitution': {'temperature': 'moderate', 'solvent': 'polar_aprotic'},
            'diels_alder': {'temperature': 'moderate_to_high', 'solvent': 'nonpolar'},
            'grignard_addition': {'temperature': 'low', 'solvent': 'ether', 'atmosphere': 'inert'},
            'reduction': {'temperature': 'low_to_moderate', 'solvent': 'protic'},
            'carbonyl_reaction': {'temperature': 'moderate', 'catalyst': 'acid_or_base'},
            'general_organic': {'temperature': 'moderate', 'solvent': 'appropriate'}
        }

        return condition_map.get(reaction_type, {'temperature': 'moderate', 'solvent': 'appropriate'})

    def _remove_duplicate_predictions(self, predictions: List[Dict]) -> List[Dict]:
        """Remove duplicate predictions based on products."""
        seen_products = set()
        unique_predictions = []

        for pred in predictions:
            # Create a signature for the products
            product_signature = tuple(sorted(pred['products']))

            if product_signature not in seen_products:
                seen_products.add(product_signature)
                unique_predictions.append(pred)
            else:
                # If duplicate, keep the one with higher confidence
                for i, existing_pred in enumerate(unique_predictions):
                    existing_signature = tuple(sorted(existing_pred['products']))
                    if existing_signature == product_signature:
                        if pred['confidence'] > existing_pred['confidence']:
                            unique_predictions[i] = pred
                        break

        return unique_predictions

    def analyze_reaction_feasibility(self, prediction: Dict) -> Dict:
        """
        Analyze the feasibility of a predicted reaction.

        Args:
            prediction: Reaction prediction dictionary

        Returns:
            Feasibility analysis
        """
        reactant_mols = [Chem.MolFromSmiles(s) for s in prediction['reactants']
                        if Chem.MolFromSmiles(s)]
        product_mols = [Chem.MolFromSmiles(s) for s in prediction['products']
                       if Chem.MolFromSmiles(s)]

        if not reactant_mols or not product_mols:
            return {'feasible': False, 'reason': 'Invalid molecules'}

        # Check atom conservation
        reactant_atoms = self._count_atoms(reactant_mols)
        product_atoms = self._count_atoms(product_mols)

        atom_balanced = reactant_atoms == product_atoms

        # Check molecular complexity using alternative method
        try:
            reactant_complexity = sum(rdMolDescriptors.BertzCT(mol) for mol in reactant_mols)
            product_complexity = sum(rdMolDescriptors.BertzCT(mol) for mol in product_mols)
        except AttributeError:
            # Fallback: use number of atoms as complexity measure
            reactant_complexity = sum(mol.GetNumAtoms() for mol in reactant_mols)
            product_complexity = sum(mol.GetNumAtoms() for mol in product_mols)

        complexity_reasonable = abs(product_complexity - reactant_complexity) < reactant_complexity * 0.5

        # Overall feasibility assessment
        feasible = atom_balanced and complexity_reasonable

        analysis = {
            'feasible': feasible,
            'atom_balanced': atom_balanced,
            'complexity_reasonable': complexity_reasonable,
            'reactant_atoms': reactant_atoms,
            'product_atoms': product_atoms,
            'confidence_adjusted': prediction['confidence'] * (0.9 if feasible else 0.5)
        }

        return analysis

    def _count_atoms(self, mols: List[Chem.Mol]) -> Dict[str, int]:
        """Count atoms by element in a list of molecules."""
        atom_count = defaultdict(int)

        for mol in mols:
            for atom in mol.GetAtoms():
                atom_count[atom.GetSymbol()] += 1

        return dict(atom_count)

    def predict_all_products(self, reactant_smiles: List[str]) -> Dict:
        """
        Complete product prediction with all methods and analysis.

        Args:
            reactant_smiles: List of SMILES strings for reactants

        Returns:
            Complete prediction results
        """
        print(f"\n🔬 Complete Product Prediction Analysis")
        print("=" * 60)
        print(f"Reactants: {' + '.join(reactant_smiles)}")

        # Get all predictions
        predictions = self.predict_products_ml_enhanced(reactant_smiles)

        if not predictions:
            return {
                'reactants': reactant_smiles,
                'predictions': [],
                'status': 'no_predictions_found'
            }

        # Analyze feasibility for each prediction
        analyzed_predictions = []
        for pred in predictions:
            feasibility = self.analyze_reaction_feasibility(pred)
            pred['feasibility'] = feasibility
            pred['final_confidence'] = feasibility['confidence_adjusted']
            analyzed_predictions.append(pred)

        # Sort by final confidence
        analyzed_predictions.sort(key=lambda x: x['final_confidence'], reverse=True)

        # Display results
        print(f"\n🎯 Found {len(analyzed_predictions)} possible reactions:")
        for i, pred in enumerate(analyzed_predictions[:3]):  # Show top 3
            print(f"\n{i+1}. {pred['reaction_type'].upper()} ({pred['method']})")
            print(f"   Products: {' + '.join(pred['products'])}")
            print(f"   Confidence: {pred['final_confidence']:.2f}")
            print(f"   Feasible: {'✅' if pred['feasibility']['feasible'] else '❌'}")
            print(f"   Description: {pred['description']}")

        results = {
            'reactants': reactant_smiles,
            'predictions': analyzed_predictions,
            'best_prediction': analyzed_predictions[0] if analyzed_predictions else None,
            'status': 'success'
        }

        print("=" * 60)

        return results
