"""
A custom, self-contained ASE-compatible calculator for PySCF.
This is used as a robust fallback if the standard ase.calculators.pyscf
module is not available in the user's installation.
"""
from ase.calculators.calculator import Calculator, all_changes
from pyscf import gto, dft

class CustomPySCF(Calculator):
    """
    A custom ASE calculator to interface with PySCF.
    """
    implemented_properties = ['energy', 'forces']

    def __init__(self, xc='b3lyp', basis='sto-3g', solvent_model=None, **kwargs):
        """
        Initializes the calculator with DFT settings.
        
        Args:
            xc (str): The exchange-correlation functional.
            basis (str): The basis set.
            solvent_model (str, optional): Name of the implicit solvent (e.g., 'Water').
        """
        super().__init__(**kwargs)
        self.xc = xc
        self.basis = basis
        self.solvent_model = solvent_model
        self.mol = None
        self.mf = None

    def calculate(self, atoms=None, properties=['energy'], system_changes=all_changes):
        """
        Performs the DFT calculation.
        
        This method is called by ASE whenever energy or forces are needed.
        """
        super().calculate(atoms, properties, system_changes)

        # Build the PySCF molecule object from the ASE atoms object
        symbols = self.atoms.get_chemical_symbols()
        positions = self.atoms.get_positions()
        mol = gto.Mole()
        mol.atom = [[sym, tuple(pos)] for sym, pos in zip(symbols, positions)]
        mol.basis = self.basis
        mol.build()
        self.mol = mol

        # Set up the DFT method
        mf = dft.RKS(mol)
        mf.xc = self.xc

        # Add solvent model if specified
        if self.solvent_model:
            mf = mf.with_solvent({'solvent_model': self.solvent_model})
        
        self.mf = mf

        # Calculate energy
        energy = self.mf.kernel()
        self.results['energy'] = energy

        # Calculate forces
        grad = self.mf.nuc_grad_method()
        forces = -grad.kernel()  # Forces are the negative of the gradient
        self.results['forces'] = forces