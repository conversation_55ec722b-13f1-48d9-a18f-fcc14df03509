# Using the surrogate engine for AI prediction
python3 main.py "ReactantA" "ReactantB" --engine surrogate

# Calculate each reactant separately
python3 main.py "ReactantA" --engine dft --xc b3lyp --basis 6-31g*
python3 main.py "ReactantB" --engine dft --xc b3lyp --basis 6-31g*


# Calculate each predicted product
python3 main.py "ProductC" --engine dft --xc b3lyp --basis 6-31g*
python3 main.py "ProductD" --engine dft --xc b3lyp --basis 6-31g*
