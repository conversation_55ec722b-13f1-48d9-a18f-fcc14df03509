"""
Builds and manages the reaction network graph.
"""
import networkx as nx

class ReactionNetwork:
    """A class to represent the reaction network using networkx."""
    
    def __init__(self):
        """Initializes an empty directed graph."""
        self.graph = nx.DiGraph()

    def add_species(self, name: str, energy: float, smiles: str):
        """
        Adds a chemical species as a node to the graph.

        Args:
            name: A unique identifier for the species (e.g., its chemical formula).
            energy: The calculated Gibbs Free Energy of the species.
            smiles: The SMILES string of the species.
        """
        self.graph.add_node(name, G=f"{energy:.2f} eV", smiles=smiles)
        print(f"Added species '{name}' to the reaction network.")

    def add_reaction(self, reactant_name: str, product_name: str, activation_energy_ev: float):
        """
        Adds a reaction as a directed edge between two species.

        Args:
            reactant_name: The name of the reactant node.
            product_name: The name of the product node.
            activation_energy_ev: The activation energy (Ea) in eV for the forward reaction.
        """
        if self.graph.has_node(reactant_name) and self.graph.has_node(product_name):
            self.graph.add_edge(reactant_name, product_name, Ea=f"{activation_energy_ev:.2f} eV")
            print(f"Added reaction edge from '{reactant_name}' to '{product_name}'.")
        else:
            print("Warning: Could not add reaction edge. One or more species not in graph.")