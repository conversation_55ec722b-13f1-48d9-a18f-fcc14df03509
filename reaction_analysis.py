"""
Advanced Reaction Analysis Tools
Implements transition state search, reaction pathway analysis, and catalyst effects.
"""

import numpy as np
from ase import Atoms
from ase.optimize import BFGS
from ase.neb import NEB
from pyscf import gto, dft
from typing import Dict, List, Optional, Tuple
import copy

# Physical constants
HARTREE_TO_EV = 27.2114
HARTREE_TO_KCAL_MOL = 627.509

class ReactionAnalyzer:
    """
    Advanced reaction analysis including transition states and pathways.
    """
    
    def __init__(self, xc: str = 'b3lyp', basis: str = '6-31g*'):
        """Initialize the reaction analyzer."""
        self.xc = xc
        self.basis = basis
    
    def estimate_activation_barrier(self, reactants: List[Atoms], products: List[Atoms]) -> Dict:
        """
        Estimate activation barrier using simple models and empirical correlations.
        
        Args:
            reactants: List of reactant ASE Atoms objects
            products: List of product ASE Atoms objects
            
        Returns:
            Dictionary containing activation barrier estimates
        """
        print("🔬 Estimating activation barrier using empirical methods...")
        
        try:
            # Calculate reactant and product energies
            reactant_energies = []
            product_energies = []
            
            for atoms in reactants:
                energy = self._calculate_single_point_energy(atoms)
                reactant_energies.append(energy)
            
            for atoms in products:
                energy = self._calculate_single_point_energy(atoms)
                product_energies.append(energy)
            
            total_reactant_energy = sum(reactant_energies)
            total_product_energy = sum(product_energies)
            
            # Reaction energy
            reaction_energy = total_product_energy - total_reactant_energy
            
            # Estimate activation barrier using Bell-Evans-Polanyi principle
            # For exothermic reactions: Ea ≈ 0.25 * |ΔE| + base_barrier
            # For endothermic reactions: Ea ≈ |ΔE| + 0.25 * |ΔE| + base_barrier
            
            base_barrier_ev = 0.5  # Typical base activation barrier in eV
            
            if reaction_energy < 0:  # Exothermic
                estimated_barrier_ev = base_barrier_ev + 0.25 * abs(reaction_energy * HARTREE_TO_EV)
            else:  # Endothermic
                estimated_barrier_ev = base_barrier_ev + abs(reaction_energy * HARTREE_TO_EV) + 0.25 * abs(reaction_energy * HARTREE_TO_EV)
            
            # Estimate rate constant using Arrhenius equation
            # k = A * exp(-Ea/RT) at 298.15 K
            temperature = 298.15  # K
            R = 8.314e-3  # kJ/mol⋅K
            A_factor = 1e13  # Typical pre-exponential factor (s⁻¹)
            
            Ea_kj_mol = estimated_barrier_ev * 96.485  # eV to kJ/mol
            rate_constant = A_factor * np.exp(-Ea_kj_mol / (R * temperature))
            
            results = {
                'reactant_energies_hartree': reactant_energies,
                'product_energies_hartree': product_energies,
                'total_reactant_energy_hartree': total_reactant_energy,
                'total_product_energy_hartree': total_product_energy,
                'reaction_energy_hartree': reaction_energy,
                'reaction_energy_ev': reaction_energy * HARTREE_TO_EV,
                'reaction_energy_kcal_mol': reaction_energy * HARTREE_TO_KCAL_MOL,
                'estimated_activation_barrier_ev': estimated_barrier_ev,
                'estimated_activation_barrier_kcal_mol': estimated_barrier_ev * 23.06,
                'estimated_rate_constant_s': rate_constant,
                'reaction_type': 'exothermic' if reaction_energy < 0 else 'endothermic',
                'temperature_k': temperature
            }
            
            print(f"✅ Reaction energy: {results['reaction_energy_ev']:.3f} eV ({results['reaction_type']})")
            print(f"   Estimated activation barrier: {results['estimated_activation_barrier_ev']:.3f} eV")
            print(f"   Estimated rate constant: {results['estimated_rate_constant_s']:.2e} s⁻¹")
            
            return results
            
        except Exception as e:
            print(f"❌ Error estimating activation barrier: {e}")
            return None
    
    def _calculate_single_point_energy(self, atoms: Atoms) -> float:
        """Calculate single-point energy for given atoms."""
        mol = gto.Mole()
        mol.atom = [[atom.symbol, atom.position] for atom in atoms]
        mol.basis = self.basis
        mol.build()
        
        mf = dft.RKS(mol)
        mf.xc = self.xc
        energy = mf.kernel()
        
        return energy
    
    def analyze_reaction_thermodynamics(self, reactants: List[Atoms], products: List[Atoms], 
                                      temperature: float = 298.15) -> Dict:
        """
        Comprehensive thermodynamic analysis of the reaction.
        
        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            temperature: Temperature in Kelvin
            
        Returns:
            Dictionary with thermodynamic analysis
        """
        print(f"🔬 Analyzing reaction thermodynamics at {temperature} K...")
        
        try:
            # Get activation barrier estimates
            barrier_results = self.estimate_activation_barrier(reactants, products)
            
            if barrier_results is None:
                return None
            
            # Calculate equilibrium constant
            reaction_energy_j = barrier_results['reaction_energy_hartree'] * 4.359744e-18  # Hartree to J
            R = 8.314  # J/mol⋅K
            K_eq = np.exp(-reaction_energy_j / (R * temperature))
            
            # Calculate half-life (assuming first-order kinetics)
            rate_constant = barrier_results['estimated_rate_constant_s']
            half_life_s = np.log(2) / rate_constant if rate_constant > 0 else float('inf')
            
            # Convert to more readable units
            if half_life_s < 60:
                half_life_str = f"{half_life_s:.2f} seconds"
            elif half_life_s < 3600:
                half_life_str = f"{half_life_s/60:.2f} minutes"
            elif half_life_s < 86400:
                half_life_str = f"{half_life_s/3600:.2f} hours"
            else:
                half_life_str = f"{half_life_s/86400:.2f} days"
            
            # Reaction feasibility assessment
            if barrier_results['reaction_energy_ev'] < -0.5:
                feasibility = "Highly favorable (strongly exothermic)"
            elif barrier_results['reaction_energy_ev'] < 0:
                feasibility = "Favorable (exothermic)"
            elif barrier_results['reaction_energy_ev'] < 0.5:
                feasibility = "Marginally favorable"
            else:
                feasibility = "Unfavorable (endothermic)"
            
            # Rate assessment
            if barrier_results['estimated_activation_barrier_ev'] < 0.5:
                rate_assessment = "Very fast (low barrier)"
            elif barrier_results['estimated_activation_barrier_ev'] < 1.0:
                rate_assessment = "Fast (moderate barrier)"
            elif barrier_results['estimated_activation_barrier_ev'] < 2.0:
                rate_assessment = "Moderate (significant barrier)"
            else:
                rate_assessment = "Slow (high barrier)"
            
            results = barrier_results.copy()
            results.update({
                'equilibrium_constant': K_eq,
                'half_life_s': half_life_s,
                'half_life_readable': half_life_str,
                'thermodynamic_feasibility': feasibility,
                'kinetic_assessment': rate_assessment,
                'temperature_k': temperature
            })
            
            print(f"✅ Equilibrium constant: {K_eq:.2e}")
            print(f"   Half-life: {half_life_str}")
            print(f"   Feasibility: {feasibility}")
            print(f"   Rate: {rate_assessment}")
            
            return results
            
        except Exception as e:
            print(f"❌ Error in thermodynamic analysis: {e}")
            return None
    
    def suggest_reaction_conditions(self, reaction_analysis: Dict) -> Dict:
        """
        Suggest optimal reaction conditions based on thermodynamic analysis.
        
        Args:
            reaction_analysis: Results from analyze_reaction_thermodynamics
            
        Returns:
            Dictionary with suggested conditions
        """
        print("🔬 Suggesting optimal reaction conditions...")
        
        if reaction_analysis is None:
            return None
        
        suggestions = {
            'temperature_suggestions': [],
            'solvent_suggestions': [],
            'catalyst_suggestions': [],
            'general_recommendations': []
        }
        
        # Temperature suggestions
        barrier_ev = reaction_analysis['estimated_activation_barrier_ev']
        reaction_energy_ev = reaction_analysis['reaction_energy_ev']
        
        if barrier_ev > 1.5:
            suggestions['temperature_suggestions'].append("Consider elevated temperature (350-400 K) to overcome high activation barrier")
        elif barrier_ev < 0.5:
            suggestions['temperature_suggestions'].append("Room temperature (298 K) should be sufficient")
        else:
            suggestions['temperature_suggestions'].append("Moderate heating (320-350 K) may be beneficial")
        
        # Solvent suggestions based on polarity
        if reaction_energy_ev < 0:  # Exothermic
            suggestions['solvent_suggestions'].append("Polar solvents may help stabilize charged intermediates")
        else:  # Endothermic
            suggestions['solvent_suggestions'].append("Consider non-polar solvents to minimize solvation effects")
        
        # Catalyst suggestions
        if barrier_ev > 1.0:
            suggestions['catalyst_suggestions'].append("Catalyst recommended to lower activation barrier")
            if reaction_energy_ev > 0:
                suggestions['catalyst_suggestions'].append("Consider acid/base catalysis for endothermic reactions")
        
        # General recommendations
        if reaction_analysis['reaction_type'] == 'endothermic':
            suggestions['general_recommendations'].append("Remove products to drive equilibrium forward")
            suggestions['general_recommendations'].append("Use excess of limiting reagent")
        else:
            suggestions['general_recommendations'].append("Control temperature to prevent side reactions")
        
        if reaction_analysis['estimated_rate_constant_s'] < 1e-6:
            suggestions['general_recommendations'].append("Long reaction times expected - consider continuous flow")
        
        print("✅ Reaction condition suggestions generated")
        return suggestions

    def analyze_catalyst_effect(self, reactants: List[Atoms], products: List[Atoms],
                               catalyst: Atoms) -> Dict:
        """
        Analyze the effect of a catalyst on the reaction.

        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            catalyst: Catalyst molecule

        Returns:
            Dictionary with catalyst analysis
        """
        print(f"🔬 Analyzing catalyst effect of {catalyst.get_chemical_formula()}...")

        try:
            # Uncatalyzed reaction
            uncatalyzed = self.estimate_activation_barrier(reactants, products)

            # Estimate catalyzed pathway
            # Simple model: catalyst binding reduces activation barrier
            catalyst_binding_energy = self._estimate_binding_energy(reactants[0], catalyst)

            # Empirical reduction in activation barrier (typically 20-80%)
            barrier_reduction_factor = 0.3 + 0.5 * abs(catalyst_binding_energy)  # 30-80% reduction
            barrier_reduction_factor = min(0.8, barrier_reduction_factor)  # Cap at 80%

            catalyzed_barrier_ev = uncatalyzed['estimated_activation_barrier_ev'] * (1 - barrier_reduction_factor)

            # Calculate rate enhancement
            temperature = 298.15
            R_ev = 8.617e-5  # Boltzmann constant in eV/K

            uncatalyzed_rate = np.exp(-uncatalyzed['estimated_activation_barrier_ev'] / (R_ev * temperature))
            catalyzed_rate = np.exp(-catalyzed_barrier_ev / (R_ev * temperature))

            rate_enhancement = catalyzed_rate / uncatalyzed_rate if uncatalyzed_rate > 0 else float('inf')

            results = {
                'catalyst_formula': catalyst.get_chemical_formula(),
                'uncatalyzed_barrier_ev': uncatalyzed['estimated_activation_barrier_ev'],
                'catalyzed_barrier_ev': catalyzed_barrier_ev,
                'barrier_reduction_ev': uncatalyzed['estimated_activation_barrier_ev'] - catalyzed_barrier_ev,
                'barrier_reduction_percent': barrier_reduction_factor * 100,
                'rate_enhancement_factor': rate_enhancement,
                'catalyst_binding_energy_ev': catalyst_binding_energy,
                'catalyst_effectiveness': self._assess_catalyst_effectiveness(rate_enhancement)
            }

            print(f"✅ Barrier reduction: {results['barrier_reduction_ev']:.3f} eV ({results['barrier_reduction_percent']:.1f}%)")
            print(f"   Rate enhancement: {results['rate_enhancement_factor']:.2e}x")
            print(f"   Effectiveness: {results['catalyst_effectiveness']}")

            return results

        except Exception as e:
            print(f"❌ Error analyzing catalyst effect: {e}")
            return None

    def _estimate_binding_energy(self, reactant: Atoms, catalyst: Atoms) -> float:
        """Estimate binding energy between reactant and catalyst."""
        try:
            # Simple model based on molecular properties
            reactant_energy = self._calculate_single_point_energy(reactant)
            catalyst_energy = self._calculate_single_point_energy(catalyst)

            # Create a simple complex (just place molecules close together)
            complex_atoms = reactant + catalyst

            # Adjust positions to avoid overlap
            for i, atom in enumerate(complex_atoms):
                if i >= len(reactant):
                    atom.position += np.array([3.0, 0.0, 0.0])  # Separate by 3 Å

            complex_energy = self._calculate_single_point_energy(complex_atoms)

            # Binding energy = E_complex - E_reactant - E_catalyst
            binding_energy = complex_energy - reactant_energy - catalyst_energy

            return binding_energy * HARTREE_TO_EV

        except:
            # Fallback: estimate based on molecular size
            return -0.1 * (len(reactant) + len(catalyst)) / 10  # Rough estimate

    def _assess_catalyst_effectiveness(self, rate_enhancement: float) -> str:
        """Assess catalyst effectiveness based on rate enhancement."""
        if rate_enhancement > 1e6:
            return "Excellent catalyst"
        elif rate_enhancement > 1e4:
            return "Very good catalyst"
        elif rate_enhancement > 1e2:
            return "Good catalyst"
        elif rate_enhancement > 10:
            return "Moderate catalyst"
        else:
            return "Poor catalyst"

    def complete_reaction_analysis(self, reactants: List[Atoms], products: List[Atoms],
                                 catalyst: Optional[Atoms] = None,
                                 temperature: float = 298.15) -> Dict:
        """
        Perform complete reaction analysis including all advanced properties.

        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            catalyst: Optional catalyst molecule
            temperature: Temperature in Kelvin

        Returns:
            Complete reaction analysis
        """
        print(f"\n🔬 Complete Reaction Analysis")
        print("=" * 60)

        results = {
            'reactants': [atoms.get_chemical_formula() for atoms in reactants],
            'products': [atoms.get_chemical_formula() for atoms in products],
            'temperature': temperature
        }

        # Basic thermodynamic analysis
        thermo_analysis = self.analyze_reaction_thermodynamics(reactants, products, temperature)
        if thermo_analysis:
            results['thermodynamics'] = thermo_analysis

        # Catalyst analysis (if provided)
        if catalyst:
            catalyst_analysis = self.analyze_catalyst_effect(reactants, products, catalyst)
            if catalyst_analysis:
                results['catalyst_analysis'] = catalyst_analysis

        # Reaction condition suggestions
        if thermo_analysis:
            suggestions = self.suggest_reaction_conditions(thermo_analysis)
            if suggestions:
                results['condition_suggestions'] = suggestions

        print("=" * 60)
        print("🎉 Complete reaction analysis finished!")

        return results
