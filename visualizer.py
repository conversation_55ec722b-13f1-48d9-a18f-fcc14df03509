"""
Visualizes reaction pathways, energy profiles, and networks.
"""
import matplotlib.pyplot as plt
import networkx as nx
from ase.neb import NEB
from ase.io import read

def plot_reaction_profile(neb: NEB):
    """
    Plots the energy profile from a NEB calculation.

    Args:
        neb: A converged ASE NEB object.
    """
    print("\nGenerating reaction profile plot...")

    # Extract energies from the NEB images
    try:
        images = neb.images
        energies = [image.get_potential_energy() for image in images]
    except Exception as e:
        print(f"Could not retrieve energies for plotting. Error: {e}")
        return
    
    # Normalize energies relative to the initial state
    reactant_energy = energies[0]
    # Convert eV to kcal/mol for more common chemical interpretation
    relative_energies = [(e - reactant_energy) * 23.0605 for e in energies] 

    fig, ax = plt.subplots()
    ax.plot(range(len(images)), relative_energies, 'o-', label="NEB Path")
    
    ax.set_xlabel('Reaction Coordinate (Image #)')
    ax.set_ylabel('Relative Energy (kcal/mol)')
    ax.set_title('Reaction Energy Profile')
    ax.grid(True)
    
    # Set ticks to correspond to image numbers
    ax.set_xticks(range(len(images)))
    ax.legend()

    plt.savefig('reaction_profile.png')
    print("Reaction profile plot saved to 'reaction_profile.png'")
    plt.show()

def plot_reaction_network(network: nx.DiGraph):
    """
    Plots the reaction network graph.

    Args:
        network: A networkx DiGraph object representing the reaction network.
    """
    if not network or network.number_of_nodes() == 0:
        print("Network is empty, skipping plot.")
        return
        
    print("\nGenerating reaction network plot...")
    # Use a layout that is better for directed graphs
    pos = nx.kamada_kawai_layout(network)
    
    plt.figure(figsize=(10, 7))
    
    # Draw nodes and edges
    nx.draw(network, pos, with_labels=True, node_size=3500, node_color='skyblue', font_size=10, font_weight='bold', arrows=True, arrowstyle='-|>', arrowsize=20)
    
    # Draw edge labels (activation energies)
    edge_labels = nx.get_edge_attributes(network, 'Ea')
    nx.draw_networkx_edge_labels(network, pos, edge_labels=edge_labels, font_color='green')
    
    # Draw node labels (Gibbs energies) below the nodes
    node_labels = {node: data.get('G', '') for node, data in network.nodes(data=True)}
    # Offset labels to be below the node center
    label_pos = {k: (v[0], v[1] - 0.15) for k, v in pos.items()} 
    nx.draw_networkx_labels(network, label_pos, labels=node_labels, font_color='darkred', font_size=9)

    plt.title('Reaction Network')
    plt.savefig('reaction_network.png')
    print("Reaction network plot saved to 'reaction_network.png'")
    plt.show()