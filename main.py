
"""
Main entry point for the Virtual Chemistry Lab.
Features a dual-engine system for DFT calculations or rapid surrogate screening.
"""
import argparse
import sys
import numpy as np

# --- Core, lightweight imports needed by both engines ---
from input_handler import smiles_to_ase
from product_predictor import predict_products
from surrogate_predictor import predict_energy_with_surrogate


def run_dft_simulation(args):
    """Encapsulates the original, high-accuracy DFT simulation workflow."""
    # --- DFT-SPECIFIC IMPORTS ---
    # These are imported ONLY when this function is called.
    print("Importing DFT modules...")
    from ase.io import write
    from collections import deque
    from molecule_optimizer import optimize_geometry, calculate_thermo_data
    from thermo_kinetics import check_thermodynamic_feasibility_G, calculate_gibbs_free_energy, calculate_activation_energy, calculate_arrhenius_rate_constant
    from reaction_pathway import setup_neb_calculation, run_neb
    from visualizer import plot_reaction_profile, plot_reaction_network
    from network_model import ReactionNetwork

    # Check if we have one reactant (single molecule analysis) or multiple (reaction analysis)
    if len(args.reactants) == 1:
        print(f"\n---🔬 Starting Single-Molecule DFT Analysis ---")
        print(f"Molecule: {args.reactants[0]}")

        # --- Single Molecule Analysis ---
        molecule_atoms = smiles_to_ase(args.reactants[0])
        if molecule_atoms is None:
            print(f"Error: Could not parse SMILES string: {args.reactants[0]}")
            return

        # Perform DFT calculation
        optimized_atoms = optimize_geometry(molecule_atoms, xc=args.xc, basis=args.basis, solvent=args.solvent)

        # Calculate thermochemical properties (with advanced properties if requested)
        advanced = getattr(args, 'advanced', False)
        temperature = getattr(args, 'temperature', 298.15)
        thermo_data = calculate_thermo_data(optimized_atoms, advanced=advanced, temperature=temperature, solvent=args.solvent)

        # Display results
        print(f"\n--- 🏁 DFT Analysis Complete ---")
        print(f"Molecule: {thermo_data['formula']}")
        print(f"Method: {thermo_data['method']}")
        if thermo_data['energy_hartree'] is not None:
            print(f"Total Energy: {thermo_data['energy_hartree']:.6f} Hartree")
            print(f"Total Energy: {thermo_data['energy_ev']:.4f} eV")
        print("-" * 40)

    else:
        # Multi-reactant analysis
        print(f"\n---🔬 Starting High-Accuracy DFT Reaction Analysis ---")
        print(f"Analyzing {len(args.reactants)} reactants: {', '.join(args.reactants)}")

        reactant_data = []
        total_reactant_energy = 0.0

        # Analyze each reactant
        for i, reactant_smiles in enumerate(args.reactants):
            print(f"\n--- Analyzing Reactant {i+1}: {reactant_smiles} ---")

            reactant_atoms = smiles_to_ase(reactant_smiles)
            if reactant_atoms is None:
                print(f"Error: Could not parse SMILES string: {reactant_smiles}")
                continue

            # Perform DFT calculation with advanced properties
            optimized_atoms = optimize_geometry(reactant_atoms, xc=args.xc, basis=args.basis, solvent=args.solvent)
            advanced = getattr(args, 'advanced', False)
            temperature = getattr(args, 'temperature', 298.15)
            thermo_data = calculate_thermo_data(optimized_atoms, advanced=advanced, temperature=temperature, solvent=args.solvent)

            reactant_data.append(thermo_data)
            if thermo_data['energy_hartree'] is not None:
                total_reactant_energy += thermo_data['energy_hartree']

        # Summary of all reactants
        print(f"\n--- 🏁 Multi-Reactant Analysis Complete ---")
        print("=" * 50)
        for i, data in enumerate(reactant_data):
            print(f"Reactant {i+1}: {data['formula']} ({args.reactants[i]})")
            if data['energy_hartree'] is not None:
                print(f"  Energy: {data['energy_hartree']:.6f} Hartree ({data['energy_ev']:.4f} eV)")

                # Show advanced properties if calculated
                if advanced and 'frontier_orbitals' in data:
                    fo = data['frontier_orbitals']
                    if fo and fo['homo_lumo_gap_ev']:
                        print(f"  HOMO-LUMO Gap: {fo['homo_lumo_gap_ev']:.3f} eV")
                        print(f"  Ionization Potential: {fo['ionization_potential_ev']:.3f} eV")

                if advanced and 'dipole_moment' in data:
                    dm = data['dipole_moment']
                    if dm and 'dipole_magnitude_debye' in dm:
                        print(f"  Dipole Moment: {dm['dipole_magnitude_debye']:.3f} Debye")

        print("=" * 50)
        print(f"Total Reactant Energy: {total_reactant_energy:.6f} Hartree ({total_reactant_energy * 27.2114:.4f} eV)")
        print(f"Method: {args.xc}/{args.basis}")

        # Perform reaction analysis if requested
        if getattr(args, 'reaction_analysis', False):
            print(f"\n🔬 Starting Advanced Reaction Analysis...")

            try:
                # Step 1: Real Product Prediction (no hardcoding!)
                from real_product_predictor import RealProductPredictor

                predictor = RealProductPredictor()
                prediction_results = predictor.predict_all_products(args.reactants)

                if prediction_results['status'] == 'success' and prediction_results['best_prediction']:
                    best_prediction = prediction_results['best_prediction']
                    predicted_products = best_prediction['products']

                    print(f"\n🎯 Best Predicted Reaction:")
                    print(f"Type: {best_prediction['reaction_type']}")
                    print(f"Products: {' + '.join(predicted_products)}")
                    print(f"Confidence: {best_prediction['final_confidence']:.2f}")

                    # Step 2: DFT Analysis of Predicted Products
                    from reaction_analysis import ReactionAnalyzer

                    # Convert to ASE Atoms objects
                    reactant_atoms_list = []
                    for smiles in args.reactants:
                        atoms = smiles_to_ase(smiles)
                        if atoms:
                            reactant_atoms_list.append(atoms)

                    product_atoms_list = []
                    for smiles in predicted_products:
                        atoms = smiles_to_ase(smiles)
                        if atoms:
                            product_atoms_list.append(atoms)

                    if reactant_atoms_list and product_atoms_list:
                        analyzer = ReactionAnalyzer(xc=args.xc, basis=args.basis)

                        # Catalyst analysis if provided
                        catalyst_atoms = None
                        if args.catalyst:
                            catalyst_atoms = smiles_to_ase(args.catalyst)

                        # Perform complete reaction analysis with REAL predicted products
                        reaction_results = analyzer.complete_reaction_analysis(
                            reactants=reactant_atoms_list,
                            products=product_atoms_list,
                            catalyst=catalyst_atoms,
                            temperature=args.temperature
                        )

                        print("\n🎯 Quantum Chemical Analysis of Predicted Reaction:")
                        if 'thermodynamics' in reaction_results:
                            thermo = reaction_results['thermodynamics']
                            print(f"Reaction Energy: {thermo['reaction_energy_ev']:.3f} eV ({thermo['reaction_type']})")
                            print(f"Activation Barrier: {thermo['estimated_activation_barrier_ev']:.3f} eV")
                            print(f"Rate Assessment: {thermo['kinetic_assessment']}")
                            print(f"Feasibility: {thermo['thermodynamic_feasibility']}")

                        if 'catalyst_analysis' in reaction_results:
                            cat = reaction_results['catalyst_analysis']
                            print(f"Catalyst Effect: {cat['barrier_reduction_percent']:.1f}% barrier reduction")
                            print(f"Rate Enhancement: {cat['rate_enhancement_factor']:.2e}x")

                        # Store complete results
                        complete_results = {
                            'product_prediction': prediction_results,
                            'quantum_analysis': reaction_results,
                            'reactant_analysis': reactant_data
                        }

                        return complete_results

                else:
                    print("⚠️  No reliable product predictions found.")
                    print("Continuing with reactant analysis only.")

            except Exception as e:
                print(f"⚠️  Could not perform reaction analysis: {e}")
                print("Continuing with basic reactant analysis only.")

        # Store results for potential reaction analysis
        return reactant_data


def run_surrogate_screening(args):
    """Performs a rapid reaction screening using the GNN surrogate model."""
    print("\n---⚡ Starting Rapid Surrogate Screening ---")

    # --- 1. Reactant Setup ---
    reactant_atoms_list = [smiles_to_ase(s) for s in args.reactants]
    if any(atom is None for atom in reactant_atoms_list):
        print("Error: Could not parse reactant SMILES.", file=sys.stderr)
        return
    
    combined_reactant_atoms = sum(reactant_atoms_list, start=reactant_atoms_list[0].__class__())

    # --- 2. Predict Product ---
    predicted_products_smiles = predict_products(args.reactants, method='transformer')
    if not predicted_products_smiles:
        print("Could not predict a product. Halting screening.", file=sys.stderr)
        return
    
    product_smiles = predicted_products_smiles[0]
    product_atoms = smiles_to_ase(product_smiles)
    if product_atoms is None:
        print(f"Could not parse predicted product SMILES: {product_smiles}", file=sys.stderr)
        return

    # --- 3. Predict Energies with Surrogate ---
    print("\nCalculating reactant energy...")
    reactant_energy = predict_energy_with_surrogate(combined_reactant_atoms)
    
    print("\nCalculating product energy...")
    product_energy = predict_energy_with_surrogate(product_atoms)

    if np.isnan(reactant_energy) or np.isnan(product_energy):
        print("Energy prediction failed for one or more species. Cannot determine reaction energy.", file=sys.stderr)
        return
        
    # --- 4. Calculate and Report Reaction Energy ---
    reaction_energy = product_energy - reactant_energy
    
    print("\n--- 🏁 Surrogate Screening Finished ---")
    print(f"Reactant(s): {', '.join(args.reactants)}")
    print(f"Predicted Product: {product_smiles}")
    print("-" * 30)
    print(f"Predicted Reaction Energy (ΔE₀): {reaction_energy:.4f} eV")
    print("-" * 30)
    if reaction_energy < 0:
        print("Result: The reaction is predicted to be EXOTHERMIC.")
    else:
        print("Result: The reaction is predicted to be ENDOTHERMIC.")

def main():
    """Main function to select the computational engine and run the appropriate task."""
    parser = argparse.ArgumentParser(
        description="Virtual Chemistry Lab - Dual Engine Simulator",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--engine', choices=['dft', 'surrogate'], default='dft',
                        help="Select the computational engine. 'dft' for high accuracy, 'surrogate' for rapid screening.")
    parser.add_argument('reactants', nargs='+', help="Initial SMILES strings of the reactant(s).")
    
    dft_group = parser.add_argument_group('DFT Engine Options')
    dft_group.add_argument('--xc', type=str, default='b3lyp', help="DFT exchange-correlation functional.")
    dft_group.add_argument('--basis', type=str, default='sto-3g', help="DFT basis set.")
    dft_group.add_argument('--solvent', type=str, default=None, help="Implicit solvent model (e.g., 'water', 'acetone', 'dmso').")
    dft_group.add_argument('--advanced', action='store_true', help="Calculate advanced properties (frequencies, charges, orbitals).")
    dft_group.add_argument('--temperature', type=float, default=298.15, help="Temperature in Kelvin for thermodynamic corrections.")
    dft_group.add_argument('--reaction-analysis', action='store_true', help="Perform complete reaction analysis (barriers, thermodynamics).")
    dft_group.add_argument('--catalyst', type=str, default=None, help="SMILES string for catalyst molecule.")
    
    args = parser.parse_args()

    print("--- 🧪 Welcome to the Ultimate Virtual Chemistry Lab 🧪 ---")
    print(f"Selected Engine: '{args.engine}'")

    if args.engine == 'dft':
        run_dft_simulation(args)
    elif args.engine == 'surrogate':
        run_surrogate_screening(args)

if __name__ == "__main__":
    main()