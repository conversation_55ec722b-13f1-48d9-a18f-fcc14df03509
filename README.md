# 🧪 Ultimate Virtual Chemistry Lab

This project is an advanced, modular virtual chemistry simulation lab built in Python. It leverages powerful quantum chemistry and cheminformatics libraries to predict the feasibility, products, kinetics, and reaction pathways of chemical reactions from first principles.

## 🎯 Core Features

-   **Reactant Input**: Accepts chemical reactants via simple SMILES strings.
-   **Product Prediction**: Predicts likely reaction products using a template-based approach (with a roadmap for AI-driven prediction).
-   **DFT Optimization**: Performs geometry optimization for all species using Density Functional Theory (DFT) via PySCF.
-   **Thermodynamic Analysis**: Calculates vibrational frequencies and Gibbs Free Energy (ΔG) to determine if a reaction is spontaneous.
-   **Pathway and Kinetics**: Models the reaction pathway using the Nudged Elastic Band (NEB) method to find the transition state and calculate the activation energy (Ea).
-   **Rate Calculation**: Estimates the reaction rate constant (k) using the Arrhenius equation.
-   **Visualization**: Automatically generates plots for the reaction energy profile and the overall reaction network.

## 📁 Project Structure

dft_gemini/
├── main.py # Main CLI entry point for all simulations
├── input_handler.py # Handles SMILES → ASE Atoms conversion
├── product_predictor.py # Predicts reaction products
├── molecule_optimizer.py # DFT geometry optimization and vibrational analysis
├── reaction_feasibility.py # (Legacy) Initial energy checks
├── reaction_pathway.py # NEB and transition state modeling
├── thermo_kinetics.py # Gibbs energy, activation energy, Arrhenius rate
├── network_model.py # Builds and manages the reaction network graph
├── visualizer.py # Generates all plots (energy profiles, networks)
├── examples/
│ └── sample_reactions.py # Example script for running simulations
├── requirements.txt # All Python dependencies
└── README.md # This documentation file



## 🛠️ Setup and Installation

Follow these steps to set up your local environment and run the lab.

**1. Clone the Repository**
```bash
git clone <your-repository-url>
cd chem_lab

# For Python 3
python -m venv venv
source venv/bin/activate  # On Windows, use `venv\Scripts\activate`

pip install -r requirements.txt

Example 1: Basic Product Prediction
This task quickly predicts the product(s) of a reaction without running the expensive quantum chemistry calculations.
Reaction: Diels-Alder (Butadiene + Ethene → Cyclohexene)
Generated bash
python main.py "C=CC=C" "C=C" --task predict_products



Example 2: Full Simulation (Thermodynamics & Kinetics)
This task runs the complete workflow: optimization, thermodynamics, pathway analysis, and visualization.
Reaction: Isomerization of Hydrogen Cyanide (HCN → CNH)
Generated bash
python main.py "[C-]#N" "[H][C-]#N" --task full_simulation```
*(Note: We provide two SMILES here representing the same atoms in different connectivity to define the start and end states for the isomerization.)*

**What Happens**:
1.  The console will log each step: geometry optimization, vibrational analysis, NEB calculation, etc.
2.  The simulation will take a few minutes to complete, as it is running real DFT calculations.
3.  Upon completion, the following files will be generated in your directory:
    -   `reaction_profile.png`: A plot of the reaction energy pathway.
    -   `reaction_network.png`: A graph visualizing the reactants and products.
    -   `neb_final_path.traj`: An ASE trajectory file of the reaction path, which can be viewed with `ase gui`.
    -   `optimization.log`, `neb.log`: Detailed log files from the simulation.

## 🔮 Future Roadmap

-   **AI Product Predictor**: Integrate specialized models (e.g., Molecular Transformers) for more accurate and generalizable product prediction.
-   **Surrogate Models**: Train fast machine learning models on DFT data to predict energies and barriers in milliseconds instead of minutes.
-   **Solvent and Catalyst Effects**: Add support for simulating reactions in different solvents and with catalytic species.
-   **Autonomous Network Exploration**: Develop a system that can automatically discover multi-step reaction pathways.




Add this to the "Core Features" section)
AI-Powered Prediction: Integrates a state-of-the-art Molecular Transformer model to predict reaction outcomes for a wide variety of chemical inputs.
(Modify the "Setup and Installation" section)
Generated markdown
## 🛠️ Setup and Installation

**3. Install Dependencies**
Install all required packages using the `requirements.txt` file. The AI model dependencies (`torch`, `transformers`) can be large.

```bash
pip install -r requirements.txt
Use code with caution.
Markdown
(Modify the "How to Run Simulations" section)
Generated markdown
## 🚀 How to Run Simulations

All simulations are run from the command line using `main.py`. The new `--predictor` flag allows you to choose the prediction engine.

---

### **Example 1: AI-Powered Product Prediction**

This is the new default. It uses a deep learning model to predict the outcome of a reaction. This example shows a Suzuki coupling, which would not work with our old template system.

**Reaction**: Bromobenzene + Phenylboronic Acid

```bash
python main.py "B(c1ccccc1)(O)O" "Brc1ccccc1" --task predict_products --predictor transformer
Use code with caution.
Markdown
(The first time you run this, it will download the AI model which may take a few minutes.)
Expected Output: The terminal will show the SMILES string of the predicted product, Biphenyl (c1ccc(cc1)c1ccccc1).
Example 2: Full Simulation with Fallback Predictor
This example runs a full simulation for a Diels-Alder reaction. We explicitly tell it to use the faster (but less general) rdkit template engine.
Reaction: Diels-Alder (Butadiene + Ethene → Cyclohexene)
Generated bash
python main.py "C=CC=C" "C=C" --task full_simulation --predictor rdkit
Use code with caution.
Bash
Generated code
This completes **Phase 2**. We have successfully integrated a powerful AI model into our workflow, making the lab significantly more intelligent and general-purpose. The system is now robust, with a clear fallback to ensure it can still function if the AI model is unavailable.

We are now ready to proceed to **Phase 3: Enhancing Scientific Realism and Control** (adding user-defined basis sets, functionals, and solvent effects) whenever you are ready.






# 🧪 Ultimate Virtual Chemistry Lab & Network Explorer

This project is an advanced, modular virtual chemistry simulation lab built in Python. It has evolved into an **autonomous reaction network explorer**. Starting from a single reactant, it uses AI prediction and quantum chemistry calculations to automatically discover and map out entire reaction pathways, including intermediates and transition states.

## 🎯 Core Features

-   **Autonomous Exploration**: The primary mode of operation. The lab intelligently explores chemical space to build a reaction network graph.
-   **AI-Powered Prediction**: Integrates a **Molecular Transformer** model to predict potential reaction products at each exploration step.
-   **First-Principles Validation**: Each potential reaction is validated with a full suite of quantum chemistry calculations:
    -   **DFT Geometry Optimization** (PySCF)
    -   **Nudged Elastic Band (NEB)** for reaction path and transition state search.
    -   **Vibrational Analysis** for zero-point energy and thermal corrections.
-   **Thermodynamic & Kinetic Analysis**: It calculates:
    -   **Gibbs Free Energy (ΔG)** to determine product stability.
    -   **Activation Energy (Ea)** to assess kinetic feasibility.
-   **User Control**: Full control over DFT accuracy (`--xc`, `--basis`), simulated environment (`--solvent`), and exploration behavior (`--max_steps`, `--ea_threshold`).
-   **Automatic Visualization**: Generates a final, comprehensive plot of the discovered reaction network, showing all species and the energy barriers connecting them.

## 🛠️ Setup and Installation

**1. Clone the Repository**
```bash
git clone <your-repository-url>
cd chem_lab

Generated markdown
## 🚀 Dual-Engine Operation: Accuracy vs. Speed

The lab now operates with two distinct computational engines. You can choose the best engine for your needs using the `--engine` flag.

### 1. DFT Engine (`--engine dft`) - **High Accuracy**

This is the default engine. It uses first-principles Density Functional Theory (DFT) for all calculations.

-   **Pros**: High accuracy, provides ground-truth results, can model reaction pathways (NEB), and calculates detailed thermodynamics (Gibbs Free Energy).
-   **Cons**: Computationally expensive. A single reaction step can take several minutes to hours.
-   **Use Case**: Detailed study of a specific reaction, finding transition states, publishing results.

**Example**:
```bash
# Runs the full, high-accuracy exploration for HCN isomerization
python main.py "[H][C-]#N" --engine dft --xc b3lyp --basis 6-31g
Use code with caution.
Markdown
2. Surrogate Engine (--engine surrogate) - Rapid Screening
This engine uses a pre-trained Graph Neural Network (SchNet) to predict molecular energies almost instantly.
Pros: Extremely fast (orders of magnitude faster than DFT). Excellent for quickly screening hundreds of potential reactions.
Cons: Less accurate than DFT. Cannot be used for geometry optimization or reaction pathway (NEB) analysis as the pre-trained model does not provide atomic forces. It predicts electronic energy (U0), not Gibbs Free Energy.
Use Case: Rapidly evaluating a list of potential reactions to see which ones are exothermic, high-throughput screening, quickly mapping out a rough energy landscape.
Example:
Generated bash
# Instantly estimates the reaction energy for a Diels-Alder reaction
python main.py "C=CC=C" "C=C" --engine surrogate```

**Expected Output for Surrogate Engine**:
The program will load the AI models, predict the product, and then immediately print the predicted reaction energy (ΔE).
Use code with caution.
Bash
--- 🏁 Surrogate Screening Finished ---
Reactant(s): C=CC=C, C=C
Predicted Product: C1=CCCC1
Predicted Reaction Energy (ΔE₀): -1.5321 eV
Result: The reaction is predicted to be EXOTHERMIC.
Generated code