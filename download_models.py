
"""
A utility script to pre-download and cache all required AI models.
"""
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
# --- FINAL CORRECTED IMPORT ---
import torchani
# --- END CORRECTION ---

def download_product_predictor():
    """Downloads the Molecular Transformer for product prediction."""
    model_name = "sagawa/ReactionT5v1-forward"
    print(f"--- Attempting to download Product Predictor: {model_name} ---")
    try:
        AutoTokenizer.from_pretrained(model_name)
        AutoModelForSeq2SeqLM.from_pretrained(model_name)
        print("✅ Product Predictor model downloaded and cached successfully!")
        return True
    except Exception as e:
        print(f"❌ ERROR: Failed to download the Product Predictor model.")
        print(f"   Details: {e}")
        return False

def download_energy_surrogate():
    """Downloads the TorchANI models."""
    print("\n--- Attempting to download Energy Surrogate (TorchANI) ---")
    try:
        # --- FINAL CORRECTED API CALL ---
        # Instantiating the model class triggers the download of the weights.
        torchani.models.ANI2x()
        # --- END CORRECTION ---
        print("✅ Energy Surrogate model downloaded and cached successfully!")
        return True
    except Exception as e:
        print(f"❌ ERROR: Failed to download the Energy Surrogate model.")
        print(f"   Details: {e}")
        return False

if __name__ == "__main__":
    print("Starting download of all required AI models...")
    product_ok = download_product_predictor()
    surrogate_ok = download_energy_surrogate()

    print("\n--- Summary ---")
    if product_ok and surrogate_ok:
        print("🎉 All models are now downloaded and cached locally.")
    else:
        print("🔥 One or more models failed to download.")